"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/Card"
import { Users, FileText, MessageCircle, ShieldOff } from "lucide-react"

interface AdminStatsProps {
  stats: {
    totalUsers: number
    totalPosts: number
    totalComments: number
    blockedUsers: number
  }
}

const AdminStats = ({ stats }: AdminStatsProps) => {
  const statCards = [
    {
      title: "Total Users",
      value: stats.totalUsers,
      icon: Users,
      color: "text-blue-600",
    },
    {
      title: "Total Posts",
      value: stats.totalPosts,
      icon: FileText,
      color: "text-green-600",
    },
    {
      title: "Total Comments",
      value: stats.totalComments,
      icon: MessageCircle,
      color: "text-purple-600",
    },
    {
      title: "Blocked Users",
      value: stats.blockedUsers,
      icon: ShieldOff,
      color: "text-red-600",
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statCards.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value.toLocaleString()}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default AdminStats
