"use client"

import { <PERSON>, use<PERSON>avigate } from "react-router-dom"
import { useAuth } from "@/contexts/AuthContext"
import { Button } from "@/components/ui/Button"
import ThemeToggle from "@/components/ThemeToggle"
import { PenTool, BookOpen, User, Settings, LogOut, Bookmark } from "lucide-react"

const Navbar = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  const handleLogout = () => {
    logout()
    navigate("/")
  }

  return (
    <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/feed" className="flex items-center space-x-2">
              <BookOpen className="h-6 w-6" />
              <span className="text-xl font-bold">BlogApp</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <ThemeToggle />

            {user && (
              <>
                <Link to="/write">
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <PenTool className="h-4 w-4" />
                    <span>Write</span>
                  </Button>
                </Link>

                <Link to="/bookmarks">
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <Bookmark className="h-4 w-4" />
                    <span>Bookmarks</span>
                  </Button>
                </Link>

                <Link to={`/profile/${user.username}`}>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </Button>
                </Link>

                <Link to="/dashboard">
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <Settings className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Button>
                </Link>

                {user.isAdmin && (
                  <Link to="/admin">
                    <Button variant="ghost" size="sm">
                      Admin
                    </Button>
                  </Link>
                )}

                <Button variant="ghost" size="sm" onClick={handleLogout} className="flex items-center space-x-2">
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
