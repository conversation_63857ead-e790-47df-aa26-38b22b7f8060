"use client"

import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Button } from "@/components/ui/Button"
import { Input } from "@/components/ui/Input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card"
import RichTextEditor from "@/components/RichTextEditor"
import TagInput from "@/components/TagInput"
import ImageUpload from "@/components/ImageUpload"
import { postsApi } from "@/lib/api"
import { Save, Send, ArrowLeft } from "lucide-react"

const WritePostPage = () => {
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [images, setImages] = useState<{ file: File; preview: string }[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const navigate = useNavigate()

  const handleImageUpload = (file: File, preview: string) => {
    setImages((prev) => [...prev, { file, preview }])
  }

  const handleImageRemove = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index))
  }

  const handleSaveDraft = async () => {
    if (!title.trim()) {
      setError("Title is required")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement draft saving
      console.log("Saving draft:", { title, content, tags, images })
      // await postsApi.saveDraft({ title, content, tags })
      alert("Draft saved successfully!")
    } catch (err) {
      setError("Failed to save draft")
    } finally {
      setIsLoading(false)
    }
  }

  const handlePublish = async () => {
    if (!title.trim()) {
      setError("Title is required")
      return
    }

    if (tags.length === 0) {
      setError("At least one tag is required")
      return
    }

    if (!content.trim()) {
      setError("Content is required")
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Upload images first, then create post
      console.log("Publishing post:", { title, content, tags, images })

      const excerpt = content.substring(0, 200) + (content.length > 200 ? "..." : "")

      await postsApi.createPost({
        title: title.trim(),
        content: content.trim(),
        tags,
      })

      navigate("/feed")
    } catch (err) {
      setError("Failed to publish post")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => navigate("/feed")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Feed
          </Button>
          <h1 className="text-3xl font-bold">Write New Post</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleSaveDraft} disabled={isLoading}>
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>
          <Button onClick={handlePublish} disabled={isLoading}>
            <Send className="w-4 h-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}

      <div className="space-y-6">
        {/* Title */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Title</CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              type="text"
              placeholder="Enter your post title..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-lg"
            />
          </CardContent>
        </Card>

        {/* Tags */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Tags</CardTitle>
          </CardHeader>
          <CardContent>
            <TagInput tags={tags} onTagsChange={setTags} placeholder="Add tags to help readers find your post..." />
          </CardContent>
        </Card>

        {/* Content Editor */}
        <RichTextEditor value={content} onChange={setContent} />

        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Images</CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={images}
              onImageUpload={handleImageUpload}
              onImageRemove={handleImageRemove}
              maxImages={5}
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button variant="outline" onClick={handleSaveDraft} disabled={isLoading}>
            <Save className="w-4 h-4 mr-2" />
            Save Draft
          </Button>
          <Button onClick={handlePublish} disabled={isLoading} size="lg">
            <Send className="w-4 h-4 mr-2" />
            {isLoading ? "Publishing..." : "Publish Post"}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default WritePostPage
