"use client"
import { <PERSON> } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import { Badge } from "@/components/ui/Badge"
import { Card, CardContent, CardHeader } from "@/components/ui/Card"
import { Heart, Bookmark, MessageCircle, Calendar, Edit, Trash2 } from "lucide-react"
import type { Post } from "@/lib/api"

interface PostsListProps {
  posts: Post[]
  showActions?: boolean
  onEdit?: (post: Post) => void
  onDelete?: (postId: string) => void
  onLike?: (postId: string) => void
  onBookmark?: (postId: string) => void
}

const PostsList = ({ posts, showActions = false, onEdit, onDelete, onLike, onBookmark }: PostsListProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No posts found.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <Card key={post.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link to={`/post/${post.id}`}>
                  <h3 className="text-xl font-semibold hover:text-primary transition-colors text-balance">
                    {post.title}
                  </h3>
                </Link>
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(post.createdAt)}
                </div>
              </div>

              {showActions && (
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" onClick={() => onEdit?.(post)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onDelete?.(post.id)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            <Link to={`/post/${post.id}`}>
              <p className="text-muted-foreground mb-4 line-clamp-3 text-pretty">{post.excerpt}</p>
            </Link>

            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  #{tag}
                </Badge>
              ))}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onLike?.(post.id)}
                  className={`flex items-center space-x-1 ${post.isLiked ? "text-red-500" : ""}`}
                >
                  <Heart className={`w-4 h-4 ${post.isLiked ? "fill-current" : ""}`} />
                  <span>{post.likes}</span>
                </Button>

                <Link to={`/post/${post.id}`}>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-1">
                    <MessageCircle className="w-4 h-4" />
                    <span>Comment</span>
                  </Button>
                </Link>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onBookmark?.(post.id)}
                className={post.isBookmarked ? "text-blue-500" : ""}
              >
                <Bookmark className={`w-4 h-4 ${post.isBookmarked ? "fill-current" : ""}`} />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default PostsList
