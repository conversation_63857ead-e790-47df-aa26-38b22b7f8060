"use client"

import { useState, useEffect } from "react"
import { useParams } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/Tabs"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import ProfileHeader from "@/components/ProfileHeader"
import PostsList from "@/components/PostsList"
import { useAuth, type User } from "@/contexts/AuthContext"
import { postsApi, type Post } from "@/lib/api"
import { Loader2 } from "lucide-react"

const ProfilePage = () => {
  const { username } = useParams<{ username: string }>()
  const { user: currentUser } = useAuth()
  const [profileUser, setProfileUser] = useState<User | null>(null)
  const [userPosts, setUserPosts] = useState<Post[]>([])
  const [bookmarkedPosts, setBookmarkedPosts] = useState<Post[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("posts")

  const isOwnProfile = currentUser?.username === username

  // Mock data
  const mockUser: User = {
    id: "1",
    username: "johndoe",
    email: "<EMAIL>",
    name: "John Doe",
    bio: "Full-stack developer passionate about React, TypeScript, and building great user experiences. Love sharing knowledge through writing and open source contributions.",
    avatar: undefined,
    isAdmin: false,
    socialLinks: {
      website: "https://johndoe.dev",
      twitter: "johndoe",
      github: "johndoe",
    },
  }

  const mockPosts: Post[] = [
    {
      id: "1",
      title: "Getting Started with React and TypeScript",
      content: "Full content here...",
      excerpt:
        "Learn how to set up a modern React application with TypeScript, including best practices for type safety and component architecture.",
      author: mockUser,
      tags: ["react", "typescript", "javascript", "frontend"],
      likes: 42,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      title: "Building Scalable APIs with Node.js",
      content: "Full content here...",
      excerpt:
        "A comprehensive guide to building robust and scalable APIs using Node.js, Express, and modern database technologies.",
      author: mockUser,
      tags: ["nodejs", "api", "backend", "express"],
      likes: 29,
      isLiked: false,
      isBookmarked: true,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z",
    },
  ]

  useEffect(() => {
    loadProfile()
  }, [username])

  const loadProfile = async () => {
    if (!username) return

    setIsLoading(true)
    try {
      // Mock API calls
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setProfileUser(mockUser)
      setUserPosts(mockPosts)
      setBookmarkedPosts(mockPosts.filter((post) => post.isBookmarked))
    } catch (error) {
      console.error("Failed to load profile:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    try {
      await postsApi.likePost(postId)
      // Update posts state
      setUserPosts((prev) =>
        prev.map((post) =>
          post.id === postId
            ? { ...post, isLiked: !post.isLiked, likes: post.isLiked ? post.likes - 1 : post.likes + 1 }
            : post,
        ),
      )
    } catch (error) {
      console.error("Failed to like post:", error)
    }
  }

  const handleBookmark = async (postId: string) => {
    try {
      await postsApi.bookmarkPost(postId)
      // Update posts state
      setUserPosts((prev) =>
        prev.map((post) => (post.id === postId ? { ...post, isBookmarked: !post.isBookmarked } : post)),
      )
    } catch (error) {
      console.error("Failed to bookmark post:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!profileUser) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <h1 className="text-2xl font-bold mb-4">User Not Found</h1>
        <p className="text-muted-foreground">The user you're looking for doesn't exist.</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Profile Header */}
      <ProfileHeader
        user={profileUser}
        isOwnProfile={isOwnProfile}
        postsCount={userPosts.length}
        onEditProfile={() => setActiveTab("settings")}
      />

      {/* Tabs */}
      <Tabs value={activeTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="posts" isActive={activeTab === "posts"} onClick={() => setActiveTab("posts")}>
            Posts ({userPosts.length})
          </TabsTrigger>
          {isOwnProfile && (
            <TabsTrigger
              value="bookmarks"
              isActive={activeTab === "bookmarks"}
              onClick={() => setActiveTab("bookmarks")}
            >
              Bookmarks ({bookmarkedPosts.length})
            </TabsTrigger>
          )}
          {isOwnProfile && (
            <TabsTrigger value="settings" isActive={activeTab === "settings"} onClick={() => setActiveTab("settings")}>
              Settings
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="posts">
          <PostsList posts={userPosts} onLike={handleLike} onBookmark={handleBookmark} />
        </TabsContent>

        {isOwnProfile && (
          <TabsContent value="bookmarks">
            <PostsList posts={bookmarkedPosts} onLike={handleLike} onBookmark={handleBookmark} />
          </TabsContent>
        )}

        {isOwnProfile && (
          <TabsContent value="settings">
            <div className="text-center py-12">
              <p className="text-muted-foreground">Profile settings will be available in the dashboard.</p>
              <Button
                variant="outline"
                className="mt-4 bg-transparent"
                onClick={() => (window.location.href = "/dashboard")}
              >
                Go to Dashboard
              </Button>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}

export default ProfilePage
