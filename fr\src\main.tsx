import React from "react"
import <PERSON>act<PERSON><PERSON> from "react-dom/client"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom"
import App from "./App.tsx"
import { AuthProvider } from "./contexts/AuthContext.tsx"
import { ThemeProvider } from "./contexts/ThemeContext.tsx"
import "./index.css"

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider>
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
