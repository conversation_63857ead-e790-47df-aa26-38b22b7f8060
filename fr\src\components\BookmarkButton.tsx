"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/Button"
import { Bookmark } from "lucide-react"
import { bookmarksApi } from "@/lib/api"

interface BookmarkButtonProps {
  postId: string
  isBookmarked: boolean
  onToggle?: (postId: string, isBookmarked: boolean) => void
  size?: "sm" | "default" | "lg"
  variant?: "ghost" | "outline" | "default"
}

const BookmarkButton = ({
  postId,
  isBookmarked: initialBookmarked,
  onToggle,
  size = "sm",
  variant = "ghost",
}: BookmarkButtonProps) => {
  const [isBookmarked, setIsBookmarked] = useState(initialBookmarked)
  const [isLoading, setIsLoading] = useState(false)

  const handleToggle = async () => {
    setIsLoading(true)
    try {
      const newBookmarkState = !isBookmarked
      setIsBookmarked(newBookmarkState)

      if (newBookmarkState) {
        await bookmarksApi.addBookmark(postId)
      } else {
        await bookmarksApi.removeBookmark(postId)
      }

      onToggle?.(postId, newBookmarkState)
    } catch (error) {
      // Revert on error
      setIsBookmarked(isBookmarked)
      console.error("Failed to toggle bookmark:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleToggle}
      disabled={isLoading}
      className={isBookmarked ? "text-blue-500" : ""}
      title={isBookmarked ? "Remove bookmark" : "Add bookmark"}
    >
      <Bookmark className={`w-4 h-4 ${isBookmarked ? "fill-current" : ""}`} />
    </Button>
  )
}

export default BookmarkButton
