"use client"

import { useState, useRef } from "react"
import { Textarea } from "@/components/ui/Textarea"
import { But<PERSON> } from "@/components/ui/Button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card"
import { Bold, Italic, Link, List, ListOrdered, Quote, Code, Eye, Edit } from "lucide-react"

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

const RichTextEditor = ({ value, onChange, placeholder = "Write your post content..." }: RichTextEditorProps) => {
  const [isPreview, setIsPreview] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const insertText = (before: string, after = "") => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = value.substring(start, end)
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end)

    onChange(newText)

    // Restore cursor position
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + before.length, end + before.length)
    }, 0)
  }

  const formatMarkdown = (text: string) => {
    // Simple markdown to HTML conversion for preview
    return text
      .replace(/^### (.*$)/gim, "<h3>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1>$1</h1>")
      .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
      .replace(/\*(.*)\*/gim, "<em>$1</em>")
      .replace(/`(.*)`/gim, "<code>$1</code>")
      .replace(/^> (.*$)/gim, "<blockquote>$1</blockquote>")
      .replace(/\n/gim, "<br>")
  }

  const toolbarButtons = [
    { icon: Bold, action: () => insertText("**", "**"), title: "Bold" },
    { icon: Italic, action: () => insertText("*", "*"), title: "Italic" },
    { icon: Link, action: () => insertText("[", "](url)"), title: "Link" },
    { icon: Quote, action: () => insertText("> "), title: "Quote" },
    { icon: Code, action: () => insertText("`", "`"), title: "Inline Code" },
    { icon: List, action: () => insertText("- "), title: "Bullet List" },
    { icon: ListOrdered, action: () => insertText("1. "), title: "Numbered List" },
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Content</CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center border rounded-md">
              <Button
                type="button"
                variant={!isPreview ? "default" : "ghost"}
                size="sm"
                onClick={() => setIsPreview(false)}
                className="rounded-r-none"
              >
                <Edit className="w-4 h-4 mr-1" />
                Write
              </Button>
              <Button
                type="button"
                variant={isPreview ? "default" : "ghost"}
                size="sm"
                onClick={() => setIsPreview(true)}
                className="rounded-l-none"
              >
                <Eye className="w-4 h-4 mr-1" />
                Preview
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isPreview && (
          <div className="flex flex-wrap gap-1 p-2 border rounded-md bg-muted/50">
            {toolbarButtons.map((button, index) => (
              <Button
                key={index}
                type="button"
                variant="ghost"
                size="sm"
                onClick={button.action}
                title={button.title}
                className="h-8 w-8 p-0"
              >
                <button.icon className="w-4 h-4" />
              </Button>
            ))}
          </div>
        )}

        {isPreview ? (
          <div
            className="min-h-[300px] p-4 border rounded-md bg-background prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ __html: formatMarkdown(value) }}
          />
        ) : (
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            className="min-h-[300px] resize-none font-mono"
          />
        )}

        <div className="text-xs text-muted-foreground">
          Supports Markdown formatting. Use the toolbar buttons or type Markdown syntax directly.
        </div>
      </CardContent>
    </Card>
  )
}

export default RichTextEditor
