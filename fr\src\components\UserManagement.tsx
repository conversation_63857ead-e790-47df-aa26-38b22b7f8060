"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import { Input } from "@/components/ui/Input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/Table"
import { Badge } from "@/components/ui/Badge"
import { adminApi } from "@/lib/api"
import { Search, Shield, ShieldOff, Calendar, Mail } from "lucide-react"

interface User {
  id: string
  username: string
  name: string
  email: string
  isBlocked: boolean
  isAdmin: boolean
  joinedAt: string
  postsCount: number
}

interface UserManagementProps {
  users: User[]
  onUserUpdate: (userId: string, action: "block" | "unblock") => void
}

const UserManagement = ({ users, onUserUpdate }: UserManagementProps) => {
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleUserAction = async (userId: string, action: "block" | "unblock") => {
    setIsLoading(true)
    try {
      if (action === "block") {
        await adminApi.blockUser(userId)
      } else {
        await adminApi.unblockUser(userId)
      }
      onUserUpdate(userId, action)
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
      alert(`Failed to ${action} user. Please try again.`)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Management</CardTitle>
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Posts</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-primary">{user.name.charAt(0).toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">@{user.username}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{user.email}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{user.postsCount}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {user.isAdmin && <Badge variant="default">Admin</Badge>}
                      <Badge variant={user.isBlocked ? "destructive" : "secondary"}>
                        {user.isBlocked ? "Blocked" : "Active"}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      {formatDate(user.joinedAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {!user.isAdmin && (
                      <Button
                        variant={user.isBlocked ? "outline" : "destructive"}
                        size="sm"
                        onClick={() => handleUserAction(user.id, user.isBlocked ? "unblock" : "block")}
                        disabled={isLoading}
                      >
                        {user.isBlocked ? (
                          <>
                            <Shield className="w-4 h-4 mr-1" />
                            Unblock
                          </>
                        ) : (
                          <>
                            <ShieldOff className="w-4 h-4 mr-1" />
                            Block
                          </>
                        )}
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No users found matching your search.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserManagement
