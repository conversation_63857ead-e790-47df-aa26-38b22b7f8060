"use client"

import { Routes, Route, Navigate } from "react-router-dom"
import { useAuth } from "./contexts/AuthContext"
import { useTheme } from "./contexts/ThemeContext"

// Pages
import LandingPage from "./pages/LandingPage"
import LoginPage from "./pages/LoginPage"
import RegisterPage from "./pages/RegisterPage"
import PostFeedPage from "./pages/PostFeedPage"
import PostDetailsPage from "./pages/PostDetailsPage"
import WritePostPage from "./pages/WritePostPage"
import BookmarksPage from "./pages/BookmarksPage"
import ProfilePage from "./pages/ProfilePage"
import DashboardPage from "./pages/DashboardPage"
import AdminDashboardPage from "./pages/AdminDashboardPage"

// Layout
import Layout from "./components/Layout"

function App() {
  const { user } = useAuth()
  const { theme } = useTheme()

  return (
    <div className={theme}>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={user ? <Navigate to="/feed" /> : <LandingPage />} />
        <Route path="/login" element={user ? <Navigate to="/feed" /> : <LoginPage />} />
        <Route path="/register" element={user ? <Navigate to="/feed" /> : <RegisterPage />} />

        {/* Protected routes */}
        <Route path="/" element={user ? <Layout /> : <Navigate to="/" />}>
          <Route path="feed" element={<PostFeedPage />} />
          <Route path="post/:id" element={<PostDetailsPage />} />
          <Route path="write" element={<WritePostPage />} />
          <Route path="bookmarks" element={<BookmarksPage />} />
          <Route path="profile/:username" element={<ProfilePage />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="admin" element={<AdminDashboardPage />} />
        </Route>
      </Routes>
    </div>
  )
}

export default App
