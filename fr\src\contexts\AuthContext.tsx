"use client"

import type React from "react"
import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, type ReactNode } from "react"

export interface User {
  id: string
  username: string
  email: string
  name: string
  bio?: string
  avatar?: string
  isAdmin: boolean
  socialLinks?: {
    twitter?: string
    github?: string
    website?: string
  }
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  register: (userData: RegisterData) => Promise<void>
  logout: () => void
  updateProfile: (userData: Partial<User>) => Promise<void>
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>
}

interface RegisterData {
  username: string
  email: string
  password: string
  name: string
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // Check for stored auth token and validate
    const token = localStorage.getItem("authToken")
    if (token) {
      // TODO: Validate token with backend
      // For now, set a mock user
      setUser({
        id: "1",
        username: "johndoe",
        email: "<EMAIL>",
        name: "John Doe",
        bio: "Software developer and blogger",
        isAdmin: false,
      })
    }
  }, [])

  const login = async (email: string, password: string) => {
    // TODO: Implement actual login API call
    console.log("Login attempt:", { email, password })

    // Mock login
    const mockUser: User = {
      id: "1",
      username: "johndoe",
      email,
      name: "John Doe",
      bio: "Software developer and blogger",
      isAdmin: email === "<EMAIL>",
    }

    setUser(mockUser)
    localStorage.setItem("authToken", "mock-token")
  }

  const register = async (userData: RegisterData) => {
    // TODO: Implement actual register API call
    console.log("Register attempt:", userData)

    // Mock registration
    const mockUser: User = {
      id: "2",
      username: userData.username,
      email: userData.email,
      name: userData.name,
      isAdmin: false,
    }

    setUser(mockUser)
    localStorage.setItem("authToken", "mock-token")
  }

  const logout = () => {
    setUser(null)
    localStorage.removeItem("authToken")
  }

  const updateProfile = async (userData: Partial<User>) => {
    // TODO: Implement actual profile update API call
    console.log("Update profile:", userData)

    if (user) {
      setUser({ ...user, ...userData })
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    // TODO: Implement actual password change API call
    console.log("Change password attempt")
  }

  const value = {
    user,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
