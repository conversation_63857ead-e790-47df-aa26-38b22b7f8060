"use client"

import { Badge } from "@/components/ui/Badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card"

interface TagsListProps {
  tags: string[]
  onTagClick: (tag: string) => void
}

const TagsList = ({ tags, onTagClick }: TagsListProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Popular Tags</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Badge
              key={tag}
              variant="outline"
              className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
              onClick={() => onTagClick(tag)}
            >
              #{tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default TagsList
