"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import PostCard from "@/components/PostCard"
import SearchBar from "@/components/SearchBar"
import TagsList from "@/components/TagsList"
import TrendingPosts from "@/components/TrendingPosts"
import { postsApi, type Post } from "@/lib/api"
import { Loader2 } from "lucide-react"

const PostFeedPage = () => {
  const [posts, setPosts] = useState<Post[]>([])
  const [trendingPosts, setTrendingPosts] = useState<Post[]>([])
  const [popularTags, setPopularTags] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")

  // Mock data for demonstration
  const mockPosts: Post[] = [
    {
      id: "1",
      title: "Getting Started with React and TypeScript",
      content: "Full content here...",
      excerpt:
        "Learn how to set up a modern React application with TypeScript, including best practices for type safety and component architecture.",
      author: {
        id: "1",
        username: "johndoe",
        name: "John Doe",
        avatar: undefined,
      },
      tags: ["react", "typescript", "javascript", "frontend"],
      likes: 42,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      title: "The Future of Web Development",
      content: "Full content here...",
      excerpt:
        "Exploring emerging trends in web development, from AI-powered tools to new frameworks that are shaping the future of the web.",
      author: {
        id: "2",
        username: "janesmith",
        name: "Jane Smith",
        avatar: undefined,
      },
      tags: ["webdev", "ai", "future", "technology"],
      likes: 38,
      isLiked: true,
      isBookmarked: true,
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z",
    },
    {
      id: "3",
      title: "Building Scalable APIs with Node.js",
      content: "Full content here...",
      excerpt:
        "A comprehensive guide to building robust and scalable APIs using Node.js, Express, and modern database technologies.",
      author: {
        id: "3",
        username: "mikejohnson",
        name: "Mike Johnson",
        avatar: undefined,
      },
      tags: ["nodejs", "api", "backend", "express"],
      likes: 29,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z",
    },
    {
      id: "4",
      title: "CSS Grid vs Flexbox: When to Use What",
      content: "Full content here...",
      excerpt:
        "Understanding the differences between CSS Grid and Flexbox, and knowing when to use each layout method for optimal results.",
      author: {
        id: "4",
        username: "sarahwilson",
        name: "Sarah Wilson",
        avatar: undefined,
      },
      tags: ["css", "grid", "flexbox", "layout"],
      likes: 56,
      isLiked: false,
      isBookmarked: true,
      createdAt: "2024-01-12T14:10:00Z",
      updatedAt: "2024-01-12T14:10:00Z",
    },
  ]

  const mockTags = [
    "react",
    "typescript",
    "javascript",
    "css",
    "nodejs",
    "python",
    "webdev",
    "ai",
    "frontend",
    "backend",
  ]

  useEffect(() => {
    loadPosts()
  }, [])

  const loadPosts = async (page = 1, search = "") => {
    try {
      setIsLoading(page === 1)

      // Mock API call - replace with actual API
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (page === 1) {
        setPosts(mockPosts)
        setTrendingPosts(mockPosts.slice(0, 3))
        setPopularTags(mockTags)
      } else {
        // Mock loading more posts
        setPosts((prev) => [...prev, ...mockPosts.map((post) => ({ ...post, id: `${post.id}-${page}` }))])
      }

      setHasMore(page < 3) // Mock pagination
      setCurrentPage(page)
    } catch (error) {
      console.error("Failed to load posts:", error)
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    loadPosts(1, query)
  }

  const handleTagClick = (tag: string) => {
    setSearchQuery(tag)
    loadPosts(1, tag)
  }

  const handleLoadMore = () => {
    setIsLoadingMore(true)
    loadPosts(currentPage + 1, searchQuery)
  }

  const handleLike = async (postId: string) => {
    try {
      // Mock API call
      await postsApi.likePost(postId)
    } catch (error) {
      console.error("Failed to like post:", error)
    }
  }

  const handleBookmark = async (postId: string) => {
    try {
      // Mock API call
      await postsApi.bookmarkPost(postId)
    } catch (error) {
      console.error("Failed to bookmark post:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Search Bar */}
      <div className="mb-8">
        <SearchBar onSearch={handleSearch} />
      </div>

      <div className="grid lg:grid-cols-5 gap-8">
        {/* Main Content - 60% */}
        <div className="lg:col-span-3">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">
              {searchQuery ? `Search results for "${searchQuery}"` : "Latest Posts"}
            </h1>
          </div>

          {posts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No posts found.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {posts.map((post) => (
                <PostCard key={post.id} post={post} onLike={handleLike} onBookmark={handleBookmark} />
              ))}

              {hasMore && (
                <div className="text-center pt-6">
                  <Button onClick={handleLoadMore} disabled={isLoadingMore} variant="outline" size="lg">
                    {isLoadingMore ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      "Load More Posts"
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Sidebar - 40% */}
        <div className="lg:col-span-2 space-y-6">
          <TagsList tags={popularTags} onTagClick={handleTagClick} />
          <TrendingPosts posts={trendingPosts} />
        </div>
      </div>
    </div>
  )
}

export default PostFeedPage
