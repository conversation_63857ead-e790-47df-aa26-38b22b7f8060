"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/Tabs"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import ProfileSettings from "@/components/ProfileSettings"
import ChangePasswordForm from "@/components/ChangePasswordForm"
import PostsList from "@/components/PostsList"
import { useAuth, type User } from "@/contexts/AuthContext"
import { postsApi, type Post } from "@/lib/api"
import { Settings, Lock, FileText, Bookmark, Edit } from "lucide-react"

const DashboardPage = () => {
  const { user, updateProfile } = useAuth()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("profile")
  const [userPosts, setUserPosts] = useState<Post[]>([])
  const [bookmarkedPosts, setBookmarkedPosts] = useState<Post[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  const mockPosts: Post[] = [
    {
      id: "1",
      title: "Getting Started with React and TypeScript",
      content: "Full content here...",
      excerpt:
        "Learn how to set up a modern React application with TypeScript, including best practices for type safety and component architecture.",
      author: {
        id: user?.id || "1",
        username: user?.username || "johndoe",
        name: user?.name || "John Doe",
        avatar: user?.avatar,
      },
      tags: ["react", "typescript", "javascript", "frontend"],
      likes: 42,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      title: "Building Scalable APIs with Node.js",
      content: "Full content here...",
      excerpt:
        "A comprehensive guide to building robust and scalable APIs using Node.js, Express, and modern database technologies.",
      author: {
        id: user?.id || "1",
        username: user?.username || "johndoe",
        name: user?.name || "John Doe",
        avatar: user?.avatar,
      },
      tags: ["nodejs", "api", "backend", "express"],
      likes: 29,
      isLiked: false,
      isBookmarked: true,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z",
    },
  ]

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      // Mock API calls
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setUserPosts(mockPosts)
      setBookmarkedPosts(mockPosts.filter((post) => post.isBookmarked))
    } catch (error) {
      console.error("Failed to load dashboard data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfileSave = async (userData: Partial<User>) => {
    try {
      await updateProfile(userData)
      alert("Profile updated successfully!")
    } catch (error) {
      console.error("Failed to update profile:", error)
      alert("Failed to update profile. Please try again.")
    }
  }

  const handleEditPost = (post: Post) => {
    // TODO: Navigate to edit post page
    console.log("Edit post:", post.id)
    navigate(`/write?edit=${post.id}`)
  }

  const handleDeletePost = async (postId: string) => {
    if (!confirm("Are you sure you want to delete this post?")) return

    try {
      await postsApi.deletePost(postId)
      setUserPosts((prev) => prev.filter((post) => post.id !== postId))
      alert("Post deleted successfully!")
    } catch (error) {
      console.error("Failed to delete post:", error)
      alert("Failed to delete post. Please try again.")
    }
  }

  const handleLike = async (postId: string) => {
    try {
      await postsApi.likePost(postId)
      setUserPosts((prev) =>
        prev.map((post) =>
          post.id === postId
            ? { ...post, isLiked: !post.isLiked, likes: post.isLiked ? post.likes - 1 : post.likes + 1 }
            : post,
        ),
      )
    } catch (error) {
      console.error("Failed to like post:", error)
    }
  }

  const handleBookmark = async (postId: string) => {
    try {
      await postsApi.bookmarkPost(postId)
      setUserPosts((prev) =>
        prev.map((post) => (post.id === postId ? { ...post, isBookmarked: !post.isBookmarked } : post)),
      )
      setBookmarkedPosts((prev) =>
        prev.filter((post) => post.id !== postId).concat(userPosts.filter((post) => post.id === postId)),
      )
    } catch (error) {
      console.error("Failed to bookmark post:", error)
    }
  }

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <p className="text-muted-foreground">Please log in to access your dashboard.</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
        <p className="text-muted-foreground">Manage your profile, posts, and account settings.</p>
      </div>

      <Tabs value={activeTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" isActive={activeTab === "profile"} onClick={() => setActiveTab("profile")}>
            <Settings className="w-4 h-4 mr-2" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="posts" isActive={activeTab === "posts"} onClick={() => setActiveTab("posts")}>
            <FileText className="w-4 h-4 mr-2" />
            My Posts ({userPosts.length})
          </TabsTrigger>
          <TabsTrigger value="bookmarks" isActive={activeTab === "bookmarks"} onClick={() => setActiveTab("bookmarks")}>
            <Bookmark className="w-4 h-4 mr-2" />
            Bookmarks ({bookmarkedPosts.length})
          </TabsTrigger>
          <TabsTrigger value="password" isActive={activeTab === "password"} onClick={() => setActiveTab("password")}>
            <Lock className="w-4 h-4 mr-2" />
            Password
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileSettings onSave={handleProfileSave} />
        </TabsContent>

        <TabsContent value="posts">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">My Posts</h2>
              <Button onClick={() => navigate("/write")}>
                <Edit className="w-4 h-4 mr-2" />
                Write New Post
              </Button>
            </div>
            <PostsList
              posts={userPosts}
              showActions={true}
              onEdit={handleEditPost}
              onDelete={handleDeletePost}
              onLike={handleLike}
              onBookmark={handleBookmark}
            />
          </div>
        </TabsContent>

        <TabsContent value="bookmarks">
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Bookmarked Posts</h2>
            <PostsList posts={bookmarkedPosts} onLike={handleLike} onBookmark={handleBookmark} />
          </div>
        </TabsContent>

        <TabsContent value="password">
          <ChangePasswordForm />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DashboardPage
