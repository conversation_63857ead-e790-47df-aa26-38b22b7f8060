"use client"

import { useState, useEffect } from "react"
import { usePara<PERSON>, useNavigate, <PERSON> } from "react-router-dom"
import { Button } from "@/components/ui/Button"
import { Badge } from "@/components/ui/Badge"
import { Card, CardContent } from "@/components/ui/Card"
import CommentSection from "@/components/CommentSection"
import { postsApi, commentsApi, type Post, type Comment } from "@/lib/api"
import { ArrowLeft, Heart, Bookmark, Calendar, Loader2 } from "lucide-react"

const PostDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [post, setPost] = useState<Post | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLiked, setIsLiked] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [likesCount, setLikesCount] = useState(0)

  // Mock post data
  const mockPost: Post = {
    id: "1",
    title: "Getting Started with React and TypeScript",
    content: `# Getting Started with React and TypeScript

React and TypeScript make a powerful combination for building modern web applications. In this comprehensive guide, we'll explore how to set up a React project with TypeScript and leverage the benefits of type safety.

## Why TypeScript with React?

TypeScript brings several advantages to React development:

- **Type Safety**: Catch errors at compile time rather than runtime
- **Better IDE Support**: Enhanced autocomplete, refactoring, and navigation
- **Self-Documenting Code**: Types serve as inline documentation
- **Easier Refactoring**: Confident code changes with type checking

## Setting Up Your Project

The easiest way to start a new React TypeScript project is using Create React App:

\`\`\`bash
npx create-react-app my-app --template typescript
cd my-app
npm start
\`\`\`

## Component Types

Here's how to type a simple React component:

\`\`\`typescript
interface Props {
  name: string;
  age?: number;
}

const UserCard: React.FC<Props> = ({ name, age }) => {
  return (
    <div>
      <h2>{name}</h2>
      {age && <p>Age: {age}</p>}
    </div>
  );
};
\`\`\`

## Hooks with TypeScript

TypeScript works seamlessly with React hooks:

\`\`\`typescript
const [count, setCount] = useState<number>(0);
const [user, setUser] = useState<User | null>(null);
\`\`\`

## Best Practices

1. **Use interfaces for props**: Define clear contracts for your components
2. **Leverage union types**: Handle different states elegantly
3. **Use generic types**: Make reusable components more flexible
4. **Enable strict mode**: Get the most out of TypeScript's type checking

## Conclusion

React and TypeScript together provide a robust foundation for building scalable applications. The initial setup investment pays dividends in code quality, maintainability, and developer experience.

Start small, gradually add types to your existing React projects, and enjoy the benefits of type-safe development!`,
    excerpt:
      "Learn how to set up a modern React application with TypeScript, including best practices for type safety and component architecture.",
    author: {
      id: "1",
      username: "johndoe",
      name: "John Doe",
      avatar: undefined,
    },
    tags: ["react", "typescript", "javascript", "frontend"],
    likes: 42,
    isLiked: false,
    isBookmarked: false,
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  }

  const mockComments: Comment[] = [
    {
      id: "1",
      content: "Great article! This really helped me understand how to properly type React components.",
      author: {
        id: "2",
        username: "janesmith",
        name: "Jane Smith",
        avatar: undefined,
      },
      createdAt: "2024-01-15T14:20:00Z",
    },
    {
      id: "2",
      content: "Thanks for the comprehensive guide. The examples are really clear and easy to follow.",
      author: {
        id: "3",
        username: "mikejohnson",
        name: "Mike Johnson",
        avatar: undefined,
      },
      createdAt: "2024-01-15T16:45:00Z",
    },
  ]

  useEffect(() => {
    loadPost()
  }, [id])

  const loadPost = async () => {
    if (!id) return

    setIsLoading(true)
    try {
      // Mock API calls
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setPost(mockPost)
      setComments(mockComments)
      setIsLiked(mockPost.isLiked)
      setIsBookmarked(mockPost.isBookmarked)
      setLikesCount(mockPost.likes)
    } catch (error) {
      console.error("Failed to load post:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLike = async () => {
    if (!post) return

    try {
      setIsLiked(!isLiked)
      setLikesCount((prev) => (isLiked ? prev - 1 : prev + 1))

      if (isLiked) {
        await postsApi.unlikePost(post.id)
      } else {
        await postsApi.likePost(post.id)
      }
    } catch (error) {
      // Revert on error
      setIsLiked(isLiked)
      setLikesCount(likesCount)
      console.error("Failed to toggle like:", error)
    }
  }

  const handleBookmark = async () => {
    if (!post) return

    try {
      setIsBookmarked(!isBookmarked)

      if (isBookmarked) {
        await postsApi.unbookmarkPost(post.id)
      } else {
        await postsApi.bookmarkPost(post.id)
      }
    } catch (error) {
      // Revert on error
      setIsBookmarked(isBookmarked)
      console.error("Failed to toggle bookmark:", error)
    }
  }

  const handleAddComment = async (content: string) => {
    if (!post) return

    try {
      const newComment = await commentsApi.createComment(post.id, content)
      setComments((prev) => [...prev, newComment])
    } catch (error) {
      console.error("Failed to add comment:", error)
      throw error
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  const formatMarkdown = (text: string) => {
    // Simple markdown to HTML conversion
    return text
      .replace(/^### (.*$)/gim, "<h3 class='text-xl font-semibold mt-6 mb-3'>$1</h3>")
      .replace(/^## (.*$)/gim, "<h2 class='text-2xl font-semibold mt-8 mb-4'>$1</h2>")
      .replace(/^# (.*$)/gim, "<h1 class='text-3xl font-bold mt-8 mb-6'>$1</h1>")
      .replace(/\*\*(.*?)\*\*/gim, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/gim, "<em>$1</em>")
      .replace(/`([^`]+)`/gim, "<code class='bg-muted px-1 py-0.5 rounded text-sm'>$1</code>")
      .replace(
        /```(\w+)?\n([\s\S]*?)```/gim,
        "<pre class='bg-muted p-4 rounded-lg overflow-x-auto my-4'><code>$2</code></pre>",
      )
      .replace(/^> (.*$)/gim, "<blockquote class='border-l-4 border-primary pl-4 italic my-4'>$1</blockquote>")
      .replace(/^\d+\. (.*$)/gim, "<li class='ml-4'>$1</li>")
      .replace(/^- (.*$)/gim, "<li class='ml-4'>$1</li>")
      .replace(/\n\n/gim, "</p><p class='mb-4'>")
      .replace(/\n/gim, "<br>")
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <h1 className="text-2xl font-bold mb-4">Post Not Found</h1>
        <p className="text-muted-foreground mb-6">The post you're looking for doesn't exist.</p>
        <Button onClick={() => navigate("/feed")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Feed
        </Button>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" size="sm" onClick={() => navigate("/feed")} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Feed
        </Button>

        <h1 className="text-4xl font-bold mb-4 text-balance">{post.title}</h1>

        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary">{post.author.name.charAt(0).toUpperCase()}</span>
              </div>
              <div>
                <Link
                  to={`/profile/${post.author.username}`}
                  className="font-medium hover:text-primary transition-colors"
                >
                  {post.author.name}
                </Link>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(post.createdAt)}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={`flex items-center space-x-1 ${isLiked ? "text-red-500" : ""}`}
            >
              <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
              <span>{likesCount}</span>
            </Button>

            <Button variant="ghost" size="sm" onClick={handleBookmark} className={isBookmarked ? "text-blue-500" : ""}>
              <Bookmark className={`w-4 h-4 ${isBookmarked ? "fill-current" : ""}`} />
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          {post.tags.map((tag) => (
            <Badge key={tag} variant="secondary">
              #{tag}
            </Badge>
          ))}
        </div>
      </div>

      {/* Content */}
      <Card className="mb-8">
        <CardContent className="pt-6">
          <div
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: `<p class='mb-4'>${formatMarkdown(post.content)}</p>` }}
          />
        </CardContent>
      </Card>

      {/* Comments */}
      <CommentSection postId={post.id} comments={comments} onAddComment={handleAddComment} />
    </div>
  )
}

export default PostDetailsPage
