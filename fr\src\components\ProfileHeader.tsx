"use client"
import { <PERSON><PERSON> } from "@/components/ui/Button"
import { Card, CardContent } from "@/components/ui/Card"
import type { User } from "@/contexts/AuthContext"
import { Calendar, Twitter, Github, Globe, Edit } from "lucide-react"

interface ProfileHeaderProps {
  user: User
  isOwnProfile: boolean
  postsCount: number
  onEditProfile?: () => void
}

const ProfileHeader = ({ user, isOwnProfile, postsCount, onEditProfile }: ProfileHeaderProps) => {
  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    })
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Avatar */}
          <div className="flex-shrink-0">
            <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center">
              {user.avatar ? (
                <img
                  src={user.avatar || "/placeholder.svg"}
                  alt={user.name}
                  className="w-24 h-24 rounded-full object-cover"
                />
              ) : (
                <span className="text-3xl font-bold text-primary">{user.name.charAt(0).toUpperCase()}</span>
              )}
            </div>
          </div>

          {/* Profile Info */}
          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold">{user.name}</h1>
                <p className="text-muted-foreground">@{user.username}</p>
                {user.bio && <p className="mt-2 text-pretty">{user.bio}</p>}

                {/* Stats */}
                <div className="flex items-center gap-4 mt-4">
                  <div className="text-sm">
                    <span className="font-semibold">{postsCount}</span>
                    <span className="text-muted-foreground ml-1">Posts</span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="w-4 h-4 mr-1" />
                    Joined {formatJoinDate("2024-01-01")}
                  </div>
                </div>

                {/* Social Links */}
                {user.socialLinks && (
                  <div className="flex items-center gap-3 mt-3">
                    {user.socialLinks.website && (
                      <a
                        href={user.socialLinks.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
                      >
                        <Globe className="w-4 h-4 mr-1" />
                        Website
                      </a>
                    )}
                    {user.socialLinks.twitter && (
                      <a
                        href={`https://twitter.com/${user.socialLinks.twitter}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
                      >
                        <Twitter className="w-4 h-4 mr-1" />
                        Twitter
                      </a>
                    )}
                    {user.socialLinks.github && (
                      <a
                        href={`https://github.com/${user.socialLinks.github}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
                      >
                        <Github className="w-4 h-4 mr-1" />
                        GitHub
                      </a>
                    )}
                  </div>
                )}
              </div>

              {/* Actions */}
              {isOwnProfile && (
                <Button variant="outline" onClick={onEditProfile}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ProfileHeader
