import { Link } from "react-router-dom"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/Card"
import { TrendingUp, Calendar } from "lucide-react"
import type { Post } from "@/lib/api"

interface TrendingPostsProps {
  posts: Post[]
}

const TrendingPosts = ({ posts }: TrendingPostsProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <TrendingUp className="w-5 h-5" />
          <span>Trending Posts</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {posts.map((post, index) => (
          <div key={post.id} className="flex space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-primary">{index + 1}</span>
            </div>
            <div className="flex-1 min-w-0">
              <Link
                to={`/post/${post.id}`}
                className="block font-medium text-sm hover:text-primary transition-colors line-clamp-2 text-balance"
              >
                {post.title}
              </Link>
              <div className="flex items-center text-xs text-muted-foreground mt-1">
                <span>{post.author.name}</span>
                <span className="mx-1">•</span>
                <Calendar className="w-3 h-3 mr-1" />
                <span>{formatDate(post.createdAt)}</span>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export default TrendingPosts
