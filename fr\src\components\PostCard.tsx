"use client"

import { useState } from "react"
import { <PERSON> } from "react-router-dom"
import { Card, CardContent, CardHeader } from "@/components/ui/Card"
import { Button } from "@/components/ui/Button"
import { Badge } from "@/components/ui/Badge"
import { Heart, Bookmark, MessageCircle, Calendar } from "lucide-react"
import type { Post } from "@/lib/api"

interface PostCardProps {
  post: Post
  onLike: (postId: string) => void
  onBookmark: (postId: string) => void
}

const PostCard = ({ post, onLike, onBookmark }: PostCardProps) => {
  const [isLiked, setIsLiked] = useState(post.isLiked)
  const [isBookmarked, setIsBookmarked] = useState(post.isBookmarked)
  const [likesCount, setLikesCount] = useState(post.likes)

  const handleLike = () => {
    setIsLiked(!isLiked)
    setLikesCount((prev) => (isLiked ? prev - 1 : prev + 1))
    onLike(post.id)
  }

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
    onBookmark(post.id)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-primary">{post.author.name.charAt(0).toUpperCase()}</span>
          </div>
          <div className="flex-1">
            <Link to={`/profile/${post.author.username}`} className="font-medium hover:text-primary transition-colors">
              {post.author.name}
            </Link>
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="w-3 h-3 mr-1" />
              {formatDate(post.createdAt)}
            </div>
          </div>
        </div>

        <Link to={`/post/${post.id}`}>
          <h3 className="text-xl font-semibold hover:text-primary transition-colors text-balance">{post.title}</h3>
        </Link>
      </CardHeader>

      <CardContent className="pt-0">
        <Link to={`/post/${post.id}`}>
          <p className="text-muted-foreground mb-4 line-clamp-3 text-pretty">{post.excerpt}</p>
        </Link>

        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              #{tag}
            </Badge>
          ))}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={`flex items-center space-x-1 ${isLiked ? "text-red-500" : ""}`}
            >
              <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
              <span>{likesCount}</span>
            </Button>

            <Link to={`/post/${post.id}`}>
              <Button variant="ghost" size="sm" className="flex items-center space-x-1">
                <MessageCircle className="w-4 h-4" />
                <span>Comment</span>
              </Button>
            </Link>
          </div>

          <Button variant="ghost" size="sm" onClick={handleBookmark} className={isBookmarked ? "text-blue-500" : ""}>
            <Bookmark className={`w-4 h-4 ${isBookmarked ? "fill-current" : ""}`} />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default PostCard
