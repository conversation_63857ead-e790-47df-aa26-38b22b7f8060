"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/Tabs"
import AdminStats from "@/components/AdminStats"
import UserManagement from "@/components/UserManagement"
import PostManagement from "@/components/PostManagement"
import { useAuth } from "@/contexts/AuthContext"
import type { Post } from "@/lib/api"
import { Shield, Users, FileText, BarChart3, Loader2 } from "lucide-react"

interface User {
  id: string
  username: string
  name: string
  email: string
  isBlocked: boolean
  isAdmin: boolean
  joinedAt: string
  postsCount: number
}

const AdminDashboardPage = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("overview")
  const [users, setUsers] = useState<User[]>([])
  const [posts, setPosts] = useState<Post[]>([])
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalPosts: 0,
    totalComments: 0,
    blockedUsers: 0,
  })
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  const mockUsers: User[] = [
    {
      id: "1",
      username: "johndoe",
      name: "John Doe",
      email: "<EMAIL>",
      isBlocked: false,
      isAdmin: false,
      joinedAt: "2024-01-01T00:00:00Z",
      postsCount: 5,
    },
    {
      id: "2",
      username: "janesmith",
      name: "Jane Smith",
      email: "<EMAIL>",
      isBlocked: false,
      isAdmin: false,
      joinedAt: "2024-01-02T00:00:00Z",
      postsCount: 3,
    },
    {
      id: "3",
      username: "mikejohnson",
      name: "Mike Johnson",
      email: "<EMAIL>",
      isBlocked: true,
      isAdmin: false,
      joinedAt: "2024-01-03T00:00:00Z",
      postsCount: 1,
    },
    {
      id: "4",
      username: "sarahwilson",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      isBlocked: false,
      isAdmin: false,
      joinedAt: "2024-01-04T00:00:00Z",
      postsCount: 7,
    },
  ]

  const mockPosts: Post[] = [
    {
      id: "1",
      title: "Getting Started with React and TypeScript",
      content: "Full content here...",
      excerpt:
        "Learn how to set up a modern React application with TypeScript, including best practices for type safety and component architecture.",
      author: {
        id: "1",
        username: "johndoe",
        name: "John Doe",
        avatar: undefined,
      },
      tags: ["react", "typescript", "javascript", "frontend"],
      likes: 42,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      title: "The Future of Web Development",
      content: "Full content here...",
      excerpt:
        "Exploring emerging trends in web development, from AI-powered tools to new frameworks that are shaping the future of the web.",
      author: {
        id: "2",
        username: "janesmith",
        name: "Jane Smith",
        avatar: undefined,
      },
      tags: ["webdev", "ai", "future", "technology"],
      likes: 38,
      isLiked: true,
      isBookmarked: true,
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z",
    },
    {
      id: "3",
      title: "Building Scalable APIs with Node.js",
      content: "Full content here...",
      excerpt:
        "A comprehensive guide to building robust and scalable APIs using Node.js, Express, and modern database technologies.",
      author: {
        id: "3",
        username: "mikejohnson",
        name: "Mike Johnson",
        avatar: undefined,
      },
      tags: ["nodejs", "api", "backend", "express"],
      likes: 29,
      isLiked: false,
      isBookmarked: false,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z",
    },
  ]

  useEffect(() => {
    // Check if user is admin
    if (!user?.isAdmin) {
      navigate("/feed")
      return
    }

    loadAdminData()
  }, [user, navigate])

  const loadAdminData = async () => {
    setIsLoading(true)
    try {
      // Mock API calls
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setUsers(mockUsers)
      setPosts(mockPosts)
      setStats({
        totalUsers: mockUsers.length,
        totalPosts: mockPosts.length,
        totalComments: 15, // Mock comment count
        blockedUsers: mockUsers.filter((u) => u.isBlocked).length,
      })
    } catch (error) {
      console.error("Failed to load admin data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUserUpdate = (userId: string, action: "block" | "unblock") => {
    setUsers((prev) => prev.map((user) => (user.id === userId ? { ...user, isBlocked: action === "block" } : user)))
    setStats((prev) => ({
      ...prev,
      blockedUsers: action === "block" ? prev.blockedUsers + 1 : prev.blockedUsers - 1,
    }))
  }

  const handlePostDelete = (postId: string) => {
    setPosts((prev) => prev.filter((post) => post.id !== postId))
    setStats((prev) => ({
      ...prev,
      totalPosts: prev.totalPosts - 1,
    }))
  }

  if (!user?.isAdmin) {
    return (
      <div className="max-w-6xl mx-auto text-center py-12">
        <Shield className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-muted-foreground">You don't have permission to access the admin dashboard.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <Shield className="w-8 h-8 text-primary" />
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>
        <p className="text-muted-foreground">Manage users, posts, and monitor platform activity.</p>
      </div>

      <Tabs value={activeTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" isActive={activeTab === "overview"} onClick={() => setActiveTab("overview")}>
            <BarChart3 className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="users" isActive={activeTab === "users"} onClick={() => setActiveTab("users")}>
            <Users className="w-4 h-4 mr-2" />
            Users ({users.length})
          </TabsTrigger>
          <TabsTrigger value="posts" isActive={activeTab === "posts"} onClick={() => setActiveTab("posts")}>
            <FileText className="w-4 h-4 mr-2" />
            Posts ({posts.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            <AdminStats stats={stats} />

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Recent Users</h3>
                <div className="space-y-2">
                  {users.slice(0, 5).map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">{user.name.charAt(0).toUpperCase()}</span>
                        </div>
                        <div>
                          <div className="font-medium text-sm">{user.name}</div>
                          <div className="text-xs text-muted-foreground">@{user.username}</div>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">{user.postsCount} posts</div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Recent Posts</h3>
                <div className="space-y-2">
                  {posts.slice(0, 5).map((post) => (
                    <div key={post.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm line-clamp-1">{post.title}</div>
                        <div className="text-xs text-muted-foreground">by {post.author.name}</div>
                      </div>
                      <div className="text-xs text-muted-foreground">{post.likes} likes</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="users">
          <UserManagement users={users} onUserUpdate={handleUserUpdate} />
        </TabsContent>

        <TabsContent value="posts">
          <PostManagement posts={posts} onPostDelete={handlePostDelete} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AdminDashboardPage
