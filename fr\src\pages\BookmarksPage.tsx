"use client"

import { useState, useEffect } from "react"
import PostsList from "@/components/PostsList"
import { postsApi, type Post } from "@/lib/api"
import { Bookmark, Loader2 } from "lucide-react"

const BookmarksPage = () => {
  const [bookmarkedPosts, setBookmarkedPosts] = useState<Post[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  const mockBookmarkedPosts: Post[] = [
    {
      id: "2",
      title: "The Future of Web Development",
      content: "Full content here...",
      excerpt:
        "Exploring emerging trends in web development, from AI-powered tools to new frameworks that are shaping the future of the web.",
      author: {
        id: "2",
        username: "jane<PERSON>",
        name: "<PERSON>",
        avatar: undefined,
      },
      tags: ["webdev", "ai", "future", "technology"],
      likes: 38,
      isLiked: true,
      isBookmarked: true,
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z",
    },
    {
      id: "4",
      title: "CSS Grid vs Flexbox: When to Use What",
      content: "Full content here...",
      excerpt:
        "Understanding the differences between CSS Grid and Flexbox, and knowing when to use each layout method for optimal results.",
      author: {
        id: "4",
        username: "sarahwilson",
        name: "Sarah Wilson",
        avatar: undefined,
      },
      tags: ["css", "grid", "flexbox", "layout"],
      likes: 56,
      isLiked: false,
      isBookmarked: true,
      createdAt: "2024-01-12T14:10:00Z",
      updatedAt: "2024-01-12T14:10:00Z",
    },
  ]

  useEffect(() => {
    loadBookmarks()
  }, [])

  const loadBookmarks = async () => {
    setIsLoading(true)
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setBookmarkedPosts(mockBookmarkedPosts)
    } catch (error) {
      console.error("Failed to load bookmarks:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLike = async (postId: string) => {
    try {
      await postsApi.likePost(postId)
      setBookmarkedPosts((prev) =>
        prev.map((post) =>
          post.id === postId
            ? { ...post, isLiked: !post.isLiked, likes: post.isLiked ? post.likes - 1 : post.likes + 1 }
            : post,
        ),
      )
    } catch (error) {
      console.error("Failed to like post:", error)
    }
  }

  const handleBookmark = async (postId: string) => {
    try {
      await postsApi.unbookmarkPost(postId)
      setBookmarkedPosts((prev) => prev.filter((post) => post.id !== postId))
    } catch (error) {
      console.error("Failed to remove bookmark:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <Bookmark className="w-8 h-8 text-primary" />
          <h1 className="text-3xl font-bold">Your Bookmarks</h1>
        </div>
        <p className="text-muted-foreground">Posts you've saved for later reading.</p>
      </div>

      {bookmarkedPosts.length === 0 ? (
        <div className="text-center py-12">
          <Bookmark className="w-16 h-16 mx-auto mb-4 text-muted-foreground/50" />
          <h2 className="text-xl font-semibold mb-2">No bookmarks yet</h2>
          <p className="text-muted-foreground">
            Start bookmarking posts you want to read later by clicking the bookmark icon on any post.
          </p>
        </div>
      ) : (
        <PostsList posts={bookmarkedPosts} onLike={handleLike} onBookmark={handleBookmark} />
      )}
    </div>
  )
}

export default BookmarksPage
