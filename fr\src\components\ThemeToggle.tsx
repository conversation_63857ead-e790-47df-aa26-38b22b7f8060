"use client"

import { But<PERSON> } from "@/components/ui/Button"
import { useTheme } from "@/contexts/ThemeContext"
import { Sun, Moon } from "lucide-react"

interface ThemeToggleProps {
  size?: "sm" | "default" | "lg" | "icon"
  variant?: "ghost" | "outline" | "default"
  showLabel?: boolean
}

const ThemeToggle = ({ size = "icon", variant = "ghost", showLabel = false }: ThemeToggleProps) => {
  const { theme, toggleTheme } = useTheme()

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleTheme}
      title={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
      className="transition-all duration-200"
    >
      {theme === "light" ? (
        <>
          <Moon className="h-4 w-4" />
          {showLabel && <span className="ml-2">Dark Mode</span>}
        </>
      ) : (
        <>
          <Sun className="h-4 w-4" />
          {showLabel && <span className="ml-2">Light Mode</span>}
        </>
      )}
    </Button>
  )
}

export default ThemeToggle
